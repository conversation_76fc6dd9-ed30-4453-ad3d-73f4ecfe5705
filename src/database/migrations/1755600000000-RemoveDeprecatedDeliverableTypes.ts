import { MigrationInterface, QueryRunner } from "typeorm";

export class RemoveDeprecatedDeliverableTypes1755600000000 implements MigrationInterface {
    name = 'RemoveDeprecatedDeliverableTypes1755600000000'

    public async up(queryRunner: QueryRunner): Promise<void> {
        // Update existing deliverables that use deprecated types to use 'PROJECT'
        await queryRunner.query(`
            UPDATE "tsc-dev"."deliverables" 
            SET "deliverableType" = 'PROJECT' 
            WHERE "deliverableType" IN ('PROJECT_YES_NO', 'MASTER_PROJECT')
        `);

        // Remove deprecated deliverable types from the deliverables_types table
        await queryRunner.query(`
            DELETE FROM "tsc-dev"."deliverables_types" 
            WHERE "code" IN ('PROJECT_YES_NO', 'MASTER_PROJECT')
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Re-add the deprecated deliverable types
        await queryRunner.query(`
            INSERT INTO "tsc-dev"."deliverables_types" ("code") 
            VALUES ('PROJECT_YES_NO'), ('MASTER_PROJECT')
        `);

        // Note: We cannot reliably restore the original deliverableType values
        // since we don't know which deliverables were originally PROJECT_YES_NO vs MASTER_PROJECT
        // This is a limitation of this migration - the down migration is not fully reversible
        console.log('Warning: Cannot fully restore original deliverableType values. All PROJECT types will remain as PROJECT.');
    }
}
